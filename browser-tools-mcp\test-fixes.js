#!/usr/bin/env node

/**
 * Test script to verify MCP connection fixes
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔧 Testing MCP Browser Tools fixes...\n');

// Test 1: Check if server is running on correct port
async function testServerConnection() {
  console.log('1. Testing server connection...');
  
  const ports = [3025, 3026];
  let serverFound = false;
  let serverPort = null;
  
  for (const port of ports) {
    try {
      const response = await fetch(`http://localhost:${port}/.identity`);
      if (response.ok) {
        const identity = await response.json();
        if (identity.signature === 'mcp-browser-connector-24x7') {
          console.log(`   ✅ Server found on port ${port}`);
          serverFound = true;
          serverPort = port;
          break;
        }
      }
    } catch (error) {
      console.log(`   ❌ Port ${port}: ${error.message}`);
    }
  }
  
  if (!serverFound) {
    console.log('   ⚠️  No server found. Please start the server first.');
    return false;
  }
  
  return serverPort;
}

// Test 2: Check extension files
function testExtensionFiles() {
  console.log('\n2. Testing extension files...');
  
  const extensionPath = path.join(__dirname, 'chrome-extension');
  const requiredFiles = [
    'manifest.json',
    'devtools.html',
    'devtools.js',
    'panel.html',
    'panel.js',
    'background.js'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    const filePath = path.join(extensionPath, file);
    if (fs.existsSync(filePath)) {
      console.log(`   ✅ ${file} exists`);
    } else {
      console.log(`   ❌ ${file} missing`);
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

// Test 3: Check port configuration in extension files
function testPortConfiguration() {
  console.log('\n3. Testing port configuration...');

  const extensionPath = path.join(__dirname, 'chrome-extension');
  const files = ['panel.js', 'devtools.js', 'panel.html'];

  let correctConfig = true;

  for (const file of files) {
    const filePath = path.join(extensionPath, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');

      // Check for correct port 3025
      if (content.includes('3025')) {
        console.log(`   ✅ ${file} configured for port 3025`);
      }

      // Check for problematic port 9009 (but allow it in comments/logic)
      if (content.includes('9009') && !content.includes('invalid (9009)')) {
        console.log(`   ❌ ${file} contains problematic port 9009`);
        correctConfig = false;
      }

      // Check for wrong port 3026 (but allow in fallback port scanning)
      if (content.includes('3026') && !content.includes('comment') && !content.includes('fallback ports')) {
        console.log(`   ⚠️  ${file} contains wrong port 3026`);
        correctConfig = false;
      }
    }
  }

  return correctConfig;
}

// Test 4: Check browser path cache clearing
function testBrowserCacheClearing() {
  console.log('\n4. Testing browser cache clearing functionality...');
  
  const serverPath = path.join(__dirname, 'browser-tools-server', 'puppeteer-service.ts');
  
  if (fs.existsSync(serverPath)) {
    const content = fs.readFileSync(serverPath, 'utf8');
    
    if (content.includes('clearBrowserCache')) {
      console.log('   ✅ Browser cache clearing function added');
      return true;
    } else {
      console.log('   ❌ Browser cache clearing function missing');
      return false;
    }
  } else {
    console.log('   ❌ Puppeteer service file not found');
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting MCP Browser Tools fix verification\n');
  
  const serverPort = await testServerConnection();
  const extensionFiles = testExtensionFiles();
  const portConfig = testPortConfiguration();
  const browserCache = testBrowserCacheClearing();
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  console.log(`Server Connection: ${serverPort ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Extension Files: ${extensionFiles ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Port Configuration: ${portConfig ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Browser Cache Fix: ${browserCache ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = serverPort && extensionFiles && portConfig && browserCache;
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! The fixes should be working correctly.');
    console.log('\n📝 Next steps:');
    console.log('1. Reload the Chrome extension');
    console.log('2. Open DevTools and check the BrowserToolsMCP panel');
    console.log('3. Use the "Auto-Discover Server" button if needed');
    console.log('4. Use "Clear Storage" button if you have connection issues');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }
  
  return allPassed;
}

// Run the tests
runTests().catch(console.error);
